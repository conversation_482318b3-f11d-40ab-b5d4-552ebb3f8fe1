# dependencies
/node_modules
/backend/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local
.env
/backend/.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# Prisma
/prisma/migrations/
/prisma/dev.db
/prisma/dev.db-journal

# Docker
/postgres_data
